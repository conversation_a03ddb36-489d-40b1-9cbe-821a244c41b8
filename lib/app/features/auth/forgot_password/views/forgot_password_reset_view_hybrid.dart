import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../../../../constants/app_color.dart';
import '../../../../constants/app_config.dart';
import '../../../../constants/app_text_styles.dart';
import '../../../../routes/route_names.dart';
import '../../../../services/navigation_service.dart';
import '../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../../../../modules/shared/support_button.dart';
import '../controllers/forgot_password_reset_controller.dart';

/// Hybrid forgot password reset view that works with both GetX and go_router
class ForgotPasswordResetViewHybrid extends StatefulWidget {
  const ForgotPasswordResetViewHybrid({super.key});

  @override
  State<ForgotPasswordResetViewHybrid> createState() =>
      _ForgotPasswordResetViewHybridState();
}

class _ForgotPasswordResetViewHybridState
    extends State<ForgotPasswordResetViewHybrid> {
  final ForgotPasswordResetController controller =
      Get.put(ForgotPasswordResetController());
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
    
    // Get arguments from go_router state
    final state = GoRouterState.of(context);
    final arguments = state.extra;
    
    if (arguments is String) {
      // Code passed as argument
      controller.code = arguments;
    } else if (arguments is Map<String, dynamic> && arguments.containsKey('arguments')) {
      // Code passed through hybrid navigation
      controller.code = arguments['arguments'] as String;
    }
  }

  @override
  void dispose() {
    Get.delete<ForgotPasswordResetController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title: const Text('Reset Password Request',
            style: AppTextStyles.titleText),
        elevation: 1,
        leading: IconButton(
          onPressed: () {
            // Use hybrid navigation for back
            navigationService.back();
          },
          icon: const Icon(Icons.arrow_back),
        ),
      ),
      floatingActionButton: const SupportButton(),
      body: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
        child: Form(
          key: controller.formKey,
          child: ListView(
            children: [
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
                child: Text(
                  'Please enter the new password',
                  style: AppTextStyles.normalText,
                ),
              ),
              TextFormField(
                controller: controller.passwordController,
                keyboardType: TextInputType.text,
                obscureText: true,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Password",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: controller.passwordConfirmController,
                keyboardType: TextInputType.text,
                obscureText: true,
                autocorrect: false,
                decoration: const InputDecoration(
                  labelText: "Confirm Password",
                  hintText: "",
                ),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Required';
                  }
                  return null;
                },
              ),
              const SizedBox(
                height: 30,
              ),
              // Reset Password button
              ElevatedButton(
                  onPressed: () async {
                    if (controller.formKey.currentState!.validate()) {
                      if (controller.passwordController.value.text !=
                          controller.passwordConfirmController.value.text) {
                        Get.dialog(
                            const KMessageDialogView(
                                content: "Incorrect password confirmation."),
                            barrierDismissible: false);
                      } else {
                        if (await controller.resetPassword(controller.code,
                            controller.passwordController.value.text)) {
                          Get.dialog(
                              KMessageDialogView(
                                  content: controller.msg,
                                  callback: () {
                                    // Use hybrid navigation to go back to guest screen
                                    navigationService.offAllNamed(RouteNames.guest);
                                  }),
                              barrierDismissible: false);
                        } else {
                          Get.dialog(
                              KMessageDialogView(content: controller.msg),
                              barrierDismissible: false);
                        }
                      }
                    }
                  },
                  style: ButtonStyle(
                      foregroundColor:
                          WidgetStateProperty.all<Color>(Colors.white),
                      backgroundColor: WidgetStateProperty.all<Color>(
                          AppColor.primaryButtonColor),
                      shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: const BorderSide(
                                  color: AppColor.primaryButtonColor)))),
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20),
                    height: 50,
                    child: const Center(
                      child: Text("Reset Password",
                          style: TextStyle(fontSize: 16)),
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import '../../../../controllers/base_screen_controller.dart';

import '../../../../services/api_client.dart';

class ForgotPasswordCheckCodeController extends BaseScreenController {
  @override
  String get screenName => 'Reset Password: Entering code';

  TextEditingController codeController = TextEditingController();
  String msg = "";
  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    codeController.dispose();
  }

  Future<bool> checkCode(String code) async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.resetPasswordCheckCode(code);
      msg = jsonDecode(response)['message'];
      EasyLoading.dismiss();
      return (jsonDecode(response)['success'] == null)
          ? false
          : jsonDecode(response)['success'];
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}

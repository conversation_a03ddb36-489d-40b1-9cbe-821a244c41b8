import 'dart:convert';

import 'package:automoment/app/controllers/base_screen_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';

import '../../../../controllers/user_controller.dart';
import '../../../../models/user_model.dart';
import '../../../../services/api_client.dart';

class VerifyEmailController extends BaseScreenController {
  @override
  String get screenName => 'Verify Email';

  String email = Get.arguments['email'] ?? '';
  String deviceName = Get.arguments['deviceName'] ?? '';

  TextEditingController codeController = TextEditingController();

  String msg = "";
  var message = "";
  var token = "";
  final formKey = GlobalKey<FormState>();

  final UserController userController = Get.put(UserController());

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
    codeController.dispose();
  }

  Future<bool> verifyEmail() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response =
          await ApiClient.verifyEmail(email, codeController.text, deviceName);
      bool success = jsonDecode(response)['success'] ?? false;
      EasyLoading.dismiss();

      if (success) {
        token = jsonDecode(response)['token'];
        message = "";
        msg = "";
        var user = User.fromJson(jsonDecode(response)['user']);

        userController.setIsLoggedIn(true);
        userController.setUser(user);
        userController.setToken(token);

        return true;
      } else {
        msg = jsonDecode(response)['message'];
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}

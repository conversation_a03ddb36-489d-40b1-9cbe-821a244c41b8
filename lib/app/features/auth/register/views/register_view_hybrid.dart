import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:email_validator/email_validator.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../constants/app_color.dart';
import '../../../../constants/app_config.dart';
import '../../../../constants/app_text_styles.dart';
import '../../../../routes/route_names.dart';
import '../../../../services/navigation_service.dart';
import '../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/register_controller.dart';

/// Hybrid register view that works with both GetX and go_router
class RegisterViewHybrid extends StatefulWidget {
  const RegisterViewHybrid({super.key});

  @override
  State<RegisterViewHybrid> createState() => _RegisterViewHybridState();
}

class _RegisterViewHybridState extends State<RegisterViewHybrid> {
  final RegisterController controller = Get.put(RegisterController());
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
  }

  @override
  void dispose() {
    Get.delete<RegisterController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: SafeArea(
        top: false,
        child: SingleChildScrollView(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          reverse: false,
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20), topRight: Radius.circular(20)),
              boxShadow: [
                BoxShadow(
                    offset: Offset(0, -5),
                    blurRadius: 15,
                    color: Colors.black12),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                getHeader(),
                buildRegisterForm(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Padding getHeader() {
    return Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            Row(
              children: [
                const Spacer(),
                IconButton(
                  onPressed: () {
                    // Use hybrid navigation for back
                    navigationService.back();
                  },
                  icon: const Icon(Icons.close),
                  iconSize: 30,
                  color: Colors.black,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Create an account",
                    style: AppTextStyles.bigHeaderText,
                  )),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: AppConfig.defaultPadding),
              child: Container(
                  alignment: Alignment.topLeft,
                  child: const Text(
                    "Sign up to get started!",
                    style: AppTextStyles.normalText,
                  )),
            ),
          ],
        ));
  }

  Padding buildRegisterForm(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
          left: AppConfig.defaultPadding * 2,
          right: AppConfig.defaultPadding * 2,
          bottom: AppConfig.defaultPadding * 2),
      child: Column(
        children: [
          Form(
            key: controller.formKey.value,
            child: Column(
              children: [
                // Full Name Field
                TextFormField(
                  controller: controller.nameController,
                  keyboardType: TextInputType.text,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Full Name",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                ),

                // Email Field
                TextFormField(
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Email",
                    hintText: "",
                  ),
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    if (!EmailValidator.validate(value)) {
                      return "Invalid email pattern";
                    }
                    return null;
                  },
                ),

                // Password Field
                Obx(
                  () => TextFormField(
                    controller: controller.passwordController,
                    keyboardType: TextInputType.text,
                    autocorrect: false,
                    obscureText: controller.isPasswordHidden.value,
                    decoration: InputDecoration(
                      labelText: "Password",
                      hintText: "",
                      suffixIcon: IconButton(
                        icon: Icon(
                          controller.isPasswordHidden.value
                              ? Icons.visibility_off
                              : Icons.visibility,
                        ),
                        onPressed: () {
                          controller.isPasswordHidden.value =
                              !controller.isPasswordHidden.value;
                        },
                      ),
                    ),
                    validator: (value) {
                      if (value!.isEmpty) {
                        return 'Required';
                      }
                      return null;
                    },
                  ),
                ),

                // Confirm Password Field
                Obx(() => TextFormField(
                      controller: controller.passwordConfirmController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      obscureText: controller.isConfirmPasswordHidden.value,
                      decoration: InputDecoration(
                        labelText: "Confirm Password",
                        hintText: "",
                        suffixIcon: IconButton(
                          icon: Icon(
                            controller.isConfirmPasswordHidden.value
                                ? Icons.visibility_off
                                : Icons.visibility,
                          ),
                          onPressed: () {
                            controller.isConfirmPasswordHidden.value =
                                !controller.isConfirmPasswordHidden.value;
                          },
                        ),
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    )),

                // Address Field
                TextFormField(
                  controller: controller.addressController,
                  keyboardType: TextInputType.text,
                  autocorrect: false,
                  decoration: const InputDecoration(
                    labelText: "Address",
                    hintText: "",
                  ),
                  validator: (value) {
                    return null;
                  },
                ),

                // Country Field
                GestureDetector(
                  onTap: () {
                    showCountryPicker(
                      context: context,
                      showPhoneCode: false,
                      favorite: ['SG', 'MY'],
                      onSelect: (Country country) {
                        controller.countryController.value.text = country.name;
                      },
                    );
                  },
                  child: AbsorbPointer(
                    child: TextFormField(
                      controller: controller.countryController.value,
                      keyboardType: TextInputType.text,
                      readOnly: true,
                      decoration: const InputDecoration(
                        labelText: "Country",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                        return null;
                      },
                    ),
                  ),
                ),

                // Mobile Number Field
                InternationalPhoneNumberInput(
                  hintText: "Mobile Number",
                  onInputChanged: (PhoneNumber number) {
                    debugPrint("mobile: ${number.phoneNumber}");
                  },
                  onInputValidated: (bool value) {
                    debugPrint("valid: $value");
                  },
                  selectorConfig: const SelectorConfig(
                    selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                  ),
                  ignoreBlank: false,
                  validator: (value) {
                    if (value!.isEmpty) {
                      return 'Required';
                    }
                    return null;
                  },
                  autoValidateMode: AutovalidateMode.disabled,
                  initialValue: controller.mobileNumber,
                  textFieldController: controller.mobileNumberController,
                  formatInput: false,
                  keyboardType: const TextInputType.numberWithOptions(
                      signed: true, decimal: true),
                  onSaved: (PhoneNumber number) {
                    debugPrint('On Saved: $number');
                  },
                ),

                // Date of Birth Field
                Obx(() {
                  return GestureDetector(
                    onTap: () {
                      DatePicker.showDatePicker(
                        context,
                        showTitleActions: true,
                        minTime: DateTime(1950, 1, 1),
                        maxTime: DateTime.now()
                            .subtract(const Duration(days: 365 * 10)),
                        onConfirm: (date) {
                          debugPrint('confirm $date');
                          controller.dateOfBirthController.value.text =
                              DateFormat('dd MMMM yyyy').format(date);
                          controller.birthDate = date;
                        },
                        currentTime: controller.birthDate,
                      );
                    },
                    child: AbsorbPointer(
                      child: TextFormField(
                        controller: controller.dateOfBirthController.value,
                        keyboardType: TextInputType.text,
                        readOnly: true,
                        decoration: const InputDecoration(
                          labelText: "Date of Birth",
                          hintText: "",
                        ),
                        validator: (value) {
                          return null;
                        },
                      ),
                    ),
                  );
                }),

                // Terms and Conditions Checkbox
                _buildTermsCheckbox(),

                const SizedBox(height: 30),

                // Sign Up Button
                _buildSignUpButton(),

                const SizedBox(height: 10),

                // Google Sign Up Button
                _buildGoogleSignUpButton(),

                const SizedBox(height: 10),

                // Apple Sign Up Button (iOS only)
                if (Platform.isIOS) _buildAppleSignUpButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsCheckbox() {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Row(children: [
        SizedBox(
          width: 24,
          height: 24,
          child: Obx(() => Checkbox(
                checkColor: Colors.white,
                activeColor: AppColor.secondaryColor,
                shape: const CircleBorder(),
                value: controller.isChecked.value,
                onChanged: (value) {
                  controller.isChecked.value = !controller.isChecked.value;
                },
              )),
        ),
        const SizedBox(width: 10),
        Flexible(
          child: Column(children: [
            RichText(
              maxLines: 2,
              text: TextSpan(children: [
                const TextSpan(
                    text: "I have read and understood Automoment's ",
                    style: TextStyle(color: Colors.black)),
                TextSpan(
                    text: "Data Protection Terms",
                    style: const TextStyle(
                        color: AppColor.secondaryColor,
                        fontWeight: FontWeight.bold),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () async {
                        debugPrint("okk");
                        Uri url = Uri.parse(
                            "https://automoment.com.sg/terms-of-service/");
                        launchInBrowser(url);
                      }),
              ]),
            ),
          ]),
        ),
      ]),
    );
  }

  Widget _buildSignUpButton() {
    return ElevatedButton(
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        onPressed: () async {
          if (controller.formKey.value.currentState!.validate()) {
            debugPrint("ok");

            if (controller.passwordController.text !=
                controller.passwordConfirmController.text) {
              Get.dialog(
                  const KMessageDialogView(
                      content: "Incorrect password confirmation."),
                  barrierDismissible: false);
            } else if (!controller.isChecked.value) {
              Get.dialog(
                  const KMessageDialogView(
                      content:
                          "You need to read and understand Automoment's Data Protection Terms."),
                  barrierDismissible: false);
            } else {
              if (await controller.register()) {
                var param = {
                  "email": controller.emailController.text,
                  "deviceName": controller.deviceName,
                };
                // Use hybrid navigation for verify email
                navigationService.toNamed(RouteNames.verifyEmail,
                    arguments: param);
              } else {
                Get.dialog(KMessageDialogView(content: controller.message),
                    barrierDismissible: false);
                debugPrint("Register failed ${controller.message}");
              }
            }
          } else {
            debugPrint("not ok");
          }
        },
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: const Center(
            child: Text("Sign Up", style: TextStyle(fontSize: 16)),
          ),
        ));
  }

  Widget _buildGoogleSignUpButton() {
    return ElevatedButton(
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        onPressed: () async {
          if (await controller.signInWithGoogle()) {
            _handleSocialLogin("Google");
          } else {
            Get.dialog(KMessageDialogView(content: controller.message),
                barrierDismissible: false);
            debugPrint("login failed ${controller.message}");
          }
        },
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: Row(
            children: [
              const Spacer(),
              Image.asset("assets/images/google.png", height: 20),
              const SizedBox(width: 10),
              const Text("Sign Up with Google", style: TextStyle(fontSize: 16)),
              const Spacer(),
            ],
          ),
        ));
  }

  Widget _buildAppleSignUpButton() {
    return ElevatedButton(
        style: ButtonStyle(
            foregroundColor: WidgetStateProperty.all<Color>(Colors.white),
            backgroundColor:
                WidgetStateProperty.all<Color>(AppColor.primaryButtonColor),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(18.0),
                    side:
                        const BorderSide(color: AppColor.primaryButtonColor)))),
        onPressed: () async {
          if (await controller.signInWithApple()) {
            _handleSocialLogin("Apple");
          } else {
            Get.dialog(KMessageDialogView(content: controller.message),
                barrierDismissible: false);
            debugPrint("login failed ${controller.message}");
          }
        },
        child: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          height: 50,
          child: Row(
            children: [
              const Spacer(),
              Image.asset("assets/images/apple.png", height: 20),
              const SizedBox(width: 10),
              const Text("Sign Up with Apple", style: TextStyle(fontSize: 16)),
              const Spacer(),
            ],
          ),
        ));
  }

  void _handleSocialLogin(String provider) {
    if (controller.isAllRequiredUserFieldsFilled()) {
      navigationService.offAllNamed(RouteNames.bottombar);
    } else {
      // Use hybrid navigation for social login form (now migrated)
      navigationService.offAllNamed(RouteNames.socialLoginForm,
          arguments: provider);
    }
  }

  Future<void> launchInBrowser(Uri url) async {
    if (!await launchUrl(
      url,
      mode: LaunchMode.externalApplication,
    )) {
      throw 'Could not launch $url';
    }
  }
}

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';
import 'package:get/get.dart';
import '../../../../controllers/base_screen_controller.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../controllers/user_controller.dart';
import '../../../../models/user_model.dart';
import '../../../../services/api_client.dart';

class SocialLoginFormController extends BaseScreenController {
  @override
  String get screenName => 'Register Form Social Login';

  final UserController userController = Get.put(UserController());
  final formKey = GlobalKey<FormState>();

  var msg = "";

  TextEditingController nameController = TextEditingController();
  var mobileNumberController = TextEditingController().obs;
  var countryController = TextEditingController().obs;
  var mobileNumber = PhoneNumber(isoCode: 'SG').obs;
  String fullPhoneNumber = "";

  String provider = Get.arguments;

  @override
  Future<void> onReady() async {
    super.onReady();
    // BaseScreenController will handle logging screen view
    super.onReady();
  }

  Future<bool> register() async {
    try {
      EasyLoading.show(status: 'Please wait...');
      var response = await ApiClient.updateSocialLoginProfile(
        userController.getToken(),
        nameController.text,
        countryController.value.text,
        mobileNumber.value.phoneNumber!,
      );

      EasyLoading.dismiss();

      bool success = jsonDecode(response)['success'];

      var user = User.fromJson(jsonDecode(response)['user']);

      userController.setUser(user);

      if (success) {
        return true;
      } else {
        msg = jsonDecode(response)['message'];
        return false;
      }
    } catch (e) {
      EasyLoading.dismiss();
      throw Exception(e.toString());
    }
  }
}

import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';

import '../../../../constants/app_color.dart';
import '../../../../constants/app_config.dart';
import '../../../../constants/app_text_styles.dart';
import '../../../../routes/route_names.dart';
import '../../../../services/navigation_service.dart';
import '../../../../modules/shared/kmessage_dialog/views/kmessage_dialog_view.dart';
import '../controllers/social_login_form_controller.dart';

class SocialLoginFormViewHybrid extends StatefulWidget {
  const SocialLoginFormViewHybrid({super.key});

  @override
  State<SocialLoginFormViewHybrid> createState() => _SocialLoginFormViewHybridState();
}

class _SocialLoginFormViewHybridState extends State<SocialLoginFormViewHybrid> {
  final SocialLoginFormController controller = Get.put(SocialLoginFormController());
  late final NavigationService navigationService;

  @override
  void initState() {
    super.initState();
    navigationService = NavigationService.instance;
  }

  @override
  void dispose() {
    Get.delete<SocialLoginFormController>();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        title:
            const Text('Additional User Data', style: AppTextStyles.titleText),
        elevation: 1,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding * 2),
          child: Column(
            children: [
              Form(
                key: controller.formKey,
                child: Column(
                  children: [
                    const Text(
                      "Please fill in the following information to complete your profile data.",
                      style: AppTextStyles.normalText,
                    ),

                    /* REJECTED BY APPLE!
                    (controller.provider == "Apple") ? TextFormField(
                      controller: controller.nameController,
                      keyboardType: TextInputType.text,
                      autocorrect: false,
                      decoration: const InputDecoration(
                        labelText: "Full Name",
                        hintText: "",
                      ),
                      validator: (value) {
                        if (value!.isEmpty) {
                          return 'Required';
                        }
                      },
                    ) : Container(),
                    */

                    GestureDetector(
                      onTap: () {
                        showCountryPicker(
                          context: context,
                          showPhoneCode: false,
                          favorite: ['SG', 'MY'],
                          onSelect: (Country country) {
                            controller.countryController.value.text =
                                country.name;
                          },
                        );
                      },
                      child: AbsorbPointer(
                        child: TextFormField(
                          controller: controller.countryController.value,
                          keyboardType: TextInputType.text,
                          //enabled: false,
                          readOnly: true,
                          decoration: const InputDecoration(
                            labelText: "Country",
                            hintText: "",
                          ),
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                    Obx(() {
                      return InternationalPhoneNumberInput(
                        hintText: "Mobile Number",
                        onInputChanged: (PhoneNumber number) {
                          debugPrint("mobile: ${number.phoneNumber}");
                          controller.mobileNumber.value = number;
                        },
                        onInputValidated: (bool value) {
                          debugPrint("valid: $value");
                        },
                        selectorConfig: const SelectorConfig(
                          selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                        ),
                        ignoreBlank: false,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return 'Required';
                          }
                          return null;
                        },
                        autoValidateMode: AutovalidateMode.disabled,
                        // selectorTextStyle: TextStyle(color: Colors.black),
                        initialValue: PhoneNumber(
                            isoCode: controller.mobileNumber.value.isoCode),
                        textFieldController:
                            controller.mobileNumberController.value,
                        formatInput: false,
                        keyboardType: const TextInputType.numberWithOptions(
                            signed: true, decimal: true),
                        // inputBorder: OutlineInputBorder(),
                        onSaved: (PhoneNumber number) {
                          debugPrint('On Saved: $number');
                        },
                      );
                    }),
                    const SizedBox(
                      height: 30,
                    ),
                    ElevatedButton(
                        style: ButtonStyle(
                            foregroundColor:
                                WidgetStateProperty.all<Color>(Colors.white),
                            backgroundColor: WidgetStateProperty.all<Color>(
                                AppColor.primaryButtonColor),
                            shape: WidgetStateProperty.all<
                                    RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(18.0),
                                    side: const BorderSide(
                                        color: AppColor.primaryButtonColor)))),
                        onPressed: () async {
                          if (controller.formKey.currentState!.validate()) {
                            debugPrint("ok");

                            if (await controller.register()) {
                              // Replace: Get.offAllNamed(Routes.BOTTOMBAR)
                              navigationService.offAllNamed(RouteNames.bottombar);
                            } else {
                              // Keep using Get.dialog as per migration guide
                              Get.dialog(
                                  KMessageDialogView(content: controller.msg),
                                  barrierDismissible: false);
                            }
                          } else {
                            debugPrint("not ok");
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.only(left: 20, right: 20),
                          height: 50,
                          child: const Center(
                            child: Text("Done", style: TextStyle(fontSize: 16)),
                          ),
                        )),
                    const SizedBox(
                      height: 30,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

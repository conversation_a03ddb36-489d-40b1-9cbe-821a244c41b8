import '../entities/auth_result.dart';
import '../entities/auth_failure.dart';
import '../entities/user.dart';

/// Repository interface for authentication operations
/// This defines the contract that the data layer must implement
abstract class AuthRepository {
  /// Login with email and password
  Future<Result<AuthResult, AuthFailure>> login({
    required String email,
    required String password,
    required String deviceName,
  });

  /// Register a new user
  Future<Result<bool, AuthFailure>> register({
    required String name,
    required String email,
    required String password,
    required String passwordConfirmation,
    required String deviceName,
    required String address,
    required String country,
    required String mobileNumber,
    DateTime? birthDate,
  });

  /// Login with Google
  Future<Result<AuthResult, AuthFailure>> loginWithGoogle({
    required String deviceName,
  });

  /// Login with Apple
  Future<Result<AuthResult, AuthFailure>> loginWithApple({
    required String deviceName,
  });

  /// Send password reset email
  Future<Result<bool, AuthFailure>> sendPasswordResetEmail({
    required String email,
  });

  /// Verify password reset code
  Future<Result<bool, AuthFailure>> verifyPasswordResetCode({
    required String code,
  });

  /// Reset password with code
  Future<Result<bool, AuthFailure>> resetPassword({
    required String code,
    required String password,
  });

  /// Verify email with code
  Future<Result<AuthResult, AuthFailure>> verifyEmail({
    required String email,
    required String code,
    required String deviceName,
  });

  /// Get current user from local storage
  Future<User?> getCurrentUser();

  /// Save user to local storage
  Future<void> saveUser(User user);

  /// Get current token from local storage
  Future<String?> getCurrentToken();

  /// Save token to local storage
  Future<void> saveToken(String token);

  /// Clear all local auth data
  Future<void> clearAuthData();

  /// Check if user is logged in
  Future<bool> isLoggedIn();
}

/// Result type for handling success and failure cases
sealed class Result<S, F> {
  const Result();
}

class Success<S, F> extends Result<S, F> {
  final S data;
  const Success(this.data);
}

class Failure<S, F> extends Result<S, F> {
  final F error;
  const Failure(this.error);
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../controllers/base_screen_controller.dart';
import '../../../../helpers/device_util.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/social_login_usecase.dart';
import 'auth_state_controller.dart';

/// Clean Architecture Login Controller using GetX
class LoginController extends BaseScreenController {
  final LoginUseCase _loginUseCase;
  final SocialLoginUseCase _socialLoginUseCase;
  final AuthStateController _authStateController;

  LoginController(
    this._loginUseCase,
    this._socialLoginUseCase,
    this._authStateController,
  );

  @override
  String get screenName => 'Login';

  // UI state
  final RxBool isPasswordHidden = true.obs;
  final RxBool isChecked = false.obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;

  // Form controllers
  final formKey = GlobalKey<FormState>();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  String? deviceName;

  @override
  Future<void> onInit() async {
    super.onInit();
    deviceName = await DeviceUtil().getDeviceName();
  }

  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  /// Execute login with email and password
  Future<bool> login() async {
    if (!formKey.currentState!.validate()) {
      return false;
    }

    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _loginUseCase.call(
        email: emailController.text.trim(),
        password: passwordController.text,
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);

        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logLogin(method: 'email');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'An unexpected error occurred';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Execute Google login
  Future<bool> signInWithGoogle() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _socialLoginUseCase.loginWithGoogle(
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);
        _authStateController.setIsLoggedInWithFirebase(true);

        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logLogin(method: 'google');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Google sign-in failed';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Execute Apple login
  Future<bool> signInWithApple() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final result = await _socialLoginUseCase.loginWithApple(
        deviceName: deviceName ?? 'Unknown Device',
      );

      if (result is Success) {
        // Update global auth state
        _authStateController.setUser(result.data.user);
        _authStateController.setToken(result.data.token);
        _authStateController.setIsLoggedIn(true);
        _authStateController.setIsLoggedInWithFirebase(true);

        if (result.data.unreadNotificationCount != null) {
          _authStateController.setUnreadNotificationCount(
            result.data.unreadNotificationCount!,
          );
        }

        // Log analytics event
        await analytics.logLogin(method: 'apple');
        await analytics.setUserProperties(
          userId: result.data.user.id.toString(),
          country: result.data.user.country,
        );

        return true;
      } else if (result is Failure) {
        errorMessage.value = result.error.message;
        return false;
      }
    } catch (e) {
      errorMessage.value = 'Apple sign-in failed';
    } finally {
      isLoading.value = false;
    }

    return false;
  }

  /// Check if user has all required fields filled
  bool isAllRequiredUserFieldsFilled() {
    return _authStateController.isAllRequiredUserFieldsFilled();
  }

  /// Toggle password visibility
  void togglePasswordVisibility() {
    isPasswordHidden.value = !isPasswordHidden.value;
  }

  /// Toggle remember me checkbox
  void toggleRememberMe() {
    isChecked.value = !isChecked.value;
  }

  /// Clear error message
  void clearError() {
    errorMessage.value = '';
  }
}
